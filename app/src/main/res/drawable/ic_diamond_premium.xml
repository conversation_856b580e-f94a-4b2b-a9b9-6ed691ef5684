<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="16dp"
    android:height="16dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- Diamond outline -->
    <path
        android:fillColor="?attr/colorr"
        android:pathData="M12,1 L19,8 L12,23 L5,8 Z" />
        
    <!-- Inner diamond highlight -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.4"
        android:pathData="M12,1 L16,7 L12,15 L8,7 Z" />
        
    <!-- Top facet reflection -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.6"
        android:pathData="M12,1 L14,5 L12,9 L10,5 Z" />
        
</vector>
