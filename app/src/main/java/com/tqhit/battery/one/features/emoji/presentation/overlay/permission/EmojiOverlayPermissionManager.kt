package com.tqhit.battery.one.features.emoji.presentation.overlay.permission

import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.view.accessibility.AccessibilityManager
import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.tqhit.battery.one.R
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.emoji.presentation.dialog.AccessibilityPermissionDialogFragment
import com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.OverlayPermissionUtils

/**
 * Manages permissions required for the emoji overlay feature.
 * Handles both accessibility service and system alert window permissions
 * with user-friendly dialogs and proper API level compatibility.
 * 
 * This manager follows the established patterns in the app:
 * - Uses existing permission utilities and dialog systems
 * - Provides clear user explanations for permission requests
 * - Handles API level differences gracefully
 * - Integrates with existing notification dialog system
 */
object EmojiOverlayPermissionManager {

    private const val TAG = "EmojiOverlayPermissionManager"
    private const val EMOJI_PERMISSION_TAG = "EmojiPermission_Check"
    private const val EMOJI_REQUEST_TAG = "EmojiPermission_Request"
    private const val EMOJI_DIALOG_TAG = "EmojiPermission_Dialog"

    // Edge case handling: Prevent multiple rapid dialog triggers
    @Volatile
    private var isDialogCurrentlyShowing = false
    private var lastDialogShowTime = 0L
    private const val MIN_DIALOG_INTERVAL_MS = 1000L // Minimum 1 second between dialog shows

    // Session-based dismissal tracking (resets on app restart)
    @Volatile
    private var isDialogDismissedThisSession = false
    private var sessionDismissalTime = 0L
    
    /**
     * Checks if the permission dialog has been dismissed in the current app session
     * @return true if the dialog has been dismissed this session
     */
    fun isDialogDismissedInCurrentSession(): Boolean {
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION: Checking if dialog dismissed this session: $isDialogDismissedThisSession")
        return isDialogDismissedThisSession
    }

    /**
     * Marks the permission dialog as dismissed for the current app session
     * This flag will reset when the app is killed and restarted
     */
    fun markDialogDismissedForSession() {
        isDialogDismissedThisSession = true
        sessionDismissalTime = System.currentTimeMillis()
        BatteryLogger.i(TAG, "EMOJI_PERMISSION: Dialog marked as dismissed for current session at ${sessionDismissalTime}ms")
    }

    /**
     * Resets the session dismissal flag (for testing purposes)
     */
    fun resetSessionDismissalFlag() {
        isDialogDismissedThisSession = false
        sessionDismissalTime = 0L
        BatteryLogger.d(TAG, "EMOJI_PERMISSION: Session dismissal flag reset")
    }

    /**
     * Checks if all required permissions are granted for the emoji overlay feature.
     *
     * @param context The application context
     * @return true if all required permissions are granted
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Checking all required emoji permissions")

        val hasAccessibilityPermission = isAccessibilityServiceEnabled(context)
        val hasOverlayPermission = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            OverlayPermissionUtils.isOverlayPermissionGranted(context)
        } else {
            true // Not needed for API 26+
        }

        val allGranted = hasAccessibilityPermission && hasOverlayPermission
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_CHECK: Accessibility: $hasAccessibilityPermission, Overlay: $hasOverlayPermission, All granted: $allGranted")

        return allGranted
    }
    
    /**
     * Checks if the emoji accessibility service is enabled for our app.
     * Handles SecurityExceptions that may occur when accessing accessibility services.
     *
     * @param context The application context
     * @return true if the emoji accessibility service is enabled
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Checking emoji accessibility service status")

        return try {
            val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as? AccessibilityManager

            if (accessibilityManager == null) {
                BatteryLogger.e(TAG, "EMOJI_PERMISSION: AccessibilityManager is null")
                return false
            }

            val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)
            val serviceName = "${context.packageName}/${EmojiBatteryAccessibilityService::class.java.name}"
            val isEnabled = enabledServices.any { it.id == serviceName }

            BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_ACCESSIBILITY_SERVICE_CHECK: enabled: $isEnabled for service: $serviceName")
            isEnabled

        } catch (securityException: SecurityException) {
            BatteryLogger.e(TAG, "EMOJI_PERMISSION: SecurityException when checking accessibility service", securityException)
            false
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "EMOJI_PERMISSION: Unexpected error when checking accessibility service", exception)
            false
        }
    }
    
    /**
     * Requests all required permissions with user-friendly explanations.
     * 
     * @param context The activity context
     * @param onAllPermissionsGranted Callback when all permissions are granted
     * @param onPermissionsDenied Callback when permissions are denied
     */
    fun requestRequiredPermissions(
        context: Context,
        onAllPermissionsGranted: () -> Unit = {},
        onPermissionsDenied: () -> Unit = {}
    ) {
        BatteryLogger.d(TAG, "Requesting required emoji permissions")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_PERMISSION_REQUEST_STARTED")

        if (hasAllRequiredPermissions(context)) {
            BatteryLogger.d(EMOJI_REQUEST_TAG, "All emoji permissions already granted")
            onAllPermissionsGranted()
            return
        }

        // Check which permissions are needed
        val needsAccessibility = !isAccessibilityServiceEnabled(context)
        val needsOverlay = Build.VERSION.SDK_INT < Build.VERSION_CODES.O &&
                          !OverlayPermissionUtils.isOverlayPermissionGranted(context)

        BatteryLogger.d(EMOJI_REQUEST_TAG, "Permission needs - accessibility: $needsAccessibility, overlay: $needsOverlay")

        when {
            needsAccessibility -> requestAccessibilityPermission(context, onAllPermissionsGranted, onPermissionsDenied)
            needsOverlay -> requestOverlayPermission(context, onAllPermissionsGranted, onPermissionsDenied)
            else -> onAllPermissionsGranted()
        }
    }
    
    /**
     * Requests emoji accessibility service permission with explanation dialog.
     */
    private fun requestAccessibilityPermission(
        context: Context,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        BatteryLogger.d(TAG, "Requesting emoji accessibility permission")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_ACCESSIBILITY_PERMISSION_REQUEST")

        val title = context.getString(R.string.emoji_overlay_accessibility_permission_title)
        val message = context.getString(R.string.emoji_overlay_accessibility_permission_message)

        BatteryLogger.d(EMOJI_DIALOG_TAG, "Showing emoji accessibility permission dialog")
        NotificationDialog(
            context = context,
            title = title,
            message = message,
            onConfirm = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User confirmed emoji accessibility permission request")
                openAccessibilitySettings(context)
                // Note: We can't directly detect when permission is granted from settings
                // The calling code should handle this in onResume or similar lifecycle method
            },
            onCancel = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User cancelled emoji accessibility permission request")
                onDenied()
            }
        ).show()
    }
    
    /**
     * Requests emoji overlay permission for older Android versions.
     */
    private fun requestOverlayPermission(
        context: Context,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        BatteryLogger.d(TAG, "Requesting emoji overlay permission for Android < 8.0")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_OVERLAY_PERMISSION_REQUEST")

        OverlayPermissionUtils.requestOverlayPermissionIfNeeded(
            context = context,
            onPermissionGranted = {
                BatteryLogger.d(EMOJI_REQUEST_TAG, "Emoji overlay permission granted, checking accessibility")
                // Check if we still need accessibility permission
                if (!isAccessibilityServiceEnabled(context)) {
                    requestAccessibilityPermission(context, onGranted, onDenied)
                } else {
                    onGranted()
                }
            },
            onPermissionDenied = {
                BatteryLogger.d(EMOJI_REQUEST_TAG, "Emoji overlay permission denied")
                onDenied()
            }
        )
    }
    
    /**
     * Opens the accessibility settings page for the user to enable the emoji service.
     */
    private fun openAccessibilitySettings(context: Context) {
        BatteryLogger.d(TAG, "Opening accessibility settings for emoji service")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_ACCESSIBILITY_SETTINGS_OPEN")

        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            BatteryLogger.d(EMOJI_REQUEST_TAG, "Emoji accessibility settings opened successfully")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error opening emoji accessibility settings", exception)

            // Fallback to general settings
            try {
                val fallbackIntent = Intent(Settings.ACTION_SETTINGS).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(fallbackIntent)
                BatteryLogger.d(EMOJI_REQUEST_TAG, "Opened general settings as fallback for emoji")
            } catch (fallbackException: Exception) {
                BatteryLogger.e(TAG, "Error opening fallback settings for emoji", fallbackException)
            }
        }
    }
    
    /**
     * Shows a comprehensive explanation dialog for why emoji permissions are needed.
     * Uses the enhanced Material 3 dialog for better user experience.
     */
    fun showPermissionExplanationDialog(
        context: Context,
        onProceed: () -> Unit = {},
        onCancel: () -> Unit = {}
    ) {
        BatteryLogger.d(TAG, "Showing enhanced emoji permission explanation dialog")
        BatteryLogger.d(EMOJI_DIALOG_TAG, "EMOJI_PERMISSION_EXPLANATION_DIALOG")

        // Check if context is a FragmentActivity to show the enhanced dialog
        if (context is FragmentActivity) {
            showEnhancedPermissionDialog(context, onProceed, onCancel)
        } else {
            // Fallback to the basic dialog for non-activity contexts
            showBasicPermissionDialog(context, onProceed, onCancel)
        }
    }

    /**
     * Shows the enhanced Material 3 permission dialog with edge case protection
     */
    private fun showEnhancedPermissionDialog(
        activity: FragmentActivity,
        onProceed: () -> Unit,
        onCancel: () -> Unit
    ) {
        try {
            // Edge case protection: Prevent multiple rapid dialog triggers
            val currentTime = System.currentTimeMillis()
            if (isDialogCurrentlyShowing) {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "Dialog already showing, ignoring duplicate request")
                return
            }

            if (currentTime - lastDialogShowTime < MIN_DIALOG_INTERVAL_MS) {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "Dialog request too soon after previous, ignoring (${currentTime - lastDialogShowTime}ms)")
                return
            }

            // Check if activity is still valid and not finishing
            if (activity.isFinishing || activity.isDestroyed) {
                BatteryLogger.w(TAG, "EMOJI_PERMISSION: Activity is finishing/destroyed, cannot show dialog")
                return
            }

            BatteryLogger.d(EMOJI_DIALOG_TAG, "Showing enhanced accessibility permission dialog")

            // Mark dialog as showing
            isDialogCurrentlyShowing = true
            lastDialogShowTime = currentTime

            val dialog = AccessibilityPermissionDialogFragment.newInstance()

            // Set up callbacks with proper cleanup
            dialog.onGrantPermissionClicked = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User confirmed enhanced permission dialog")
                isDialogCurrentlyShowing = false
                onProceed()
            }

            dialog.onCancelClicked = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User cancelled enhanced permission dialog")
                isDialogCurrentlyShowing = false
                // Session dismissal is now handled in the dialog fragment itself
                onCancel()
            }

            dialog.onLearnMoreClicked = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User clicked Learn More in enhanced dialog")
                // Could open help documentation or show additional info
            }

            // Show the dialog
            dialog.show(activity.supportFragmentManager, "AccessibilityPermissionDialog")

        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error showing enhanced permission dialog, falling back to basic", exception)
            isDialogCurrentlyShowing = false // Reset flag on error
            showBasicPermissionDialog(activity, onProceed, onCancel)
        }
    }

    /**
     * Shows the basic permission dialog as fallback
     */
    private fun showBasicPermissionDialog(
        context: Context,
        onProceed: () -> Unit,
        onCancel: () -> Unit
    ) {
        BatteryLogger.d(EMOJI_DIALOG_TAG, "Showing basic permission dialog as fallback")

        val title = context.getString(R.string.emoji_overlay_permission_explanation_title)
        val message = buildPermissionExplanationMessage(context)

        NotificationDialog(
            context = context,
            title = title,
            message = message,
            onConfirm = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User confirmed basic permission dialog")
                onProceed()
            },
            onCancel = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User cancelled basic permission dialog")
                onCancel()
            }
        ).show()
    }
    
    /**
     * Builds a detailed explanation message for permission requirements.
     */
    private fun buildPermissionExplanationMessage(context: Context): String {
        val baseMessage = context.getString(R.string.emoji_overlay_permission_explanation_message)
        
        val permissionsNeeded = mutableListOf<String>()
        
        // Always need accessibility service
        permissionsNeeded.add(context.getString(R.string.emoji_overlay_accessibility_permission_explanation))
        
        // Only need overlay permission for older Android versions
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            permissionsNeeded.add(context.getString(R.string.emoji_overlay_overlay_permission_explanation))
        }
        
        val permissionsList = permissionsNeeded.joinToString("\n\n• ", "\n\n• ")
        
        return "$baseMessage$permissionsList"
    }
    
    /**
     * Gets debug information about the current session state
     */
    fun getSessionDebugInfo(): String {
        return "SessionState{dismissed=$isDialogDismissedThisSession, dismissalTime=$sessionDismissalTime, currentTime=${System.currentTimeMillis()}}"
    }

    /**
     * Checks if the permission dialog should be shown considering both permissions and session dismissal
     * @param context The application context
     * @param respectSessionDismissal Whether to respect session dismissal flag
     * @return true if dialog should be shown
     */
    fun shouldShowPermissionDialog(context: Context, respectSessionDismissal: Boolean = true): Boolean {
        val hasPermissions = hasAllRequiredPermissions(context)
        val isDismissedThisSession = isDialogDismissedInCurrentSession()

        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION: shouldShowPermissionDialog check - hasPermissions: $hasPermissions, respectSessionDismissal: $respectSessionDismissal, isDismissedThisSession: $isDismissedThisSession")
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION: Session debug info: ${getSessionDebugInfo()}")

        // First check if permissions are already granted
        if (hasPermissions) {
            BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION: All permissions granted, no dialog needed")
            return false
        }

        // Check session dismissal if requested
        if (respectSessionDismissal && isDismissedThisSession) {
            BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION: Dialog dismissed this session, not showing again")
            return false
        }

        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION: Dialog should be shown - permissions missing and conditions met")
        return true
    }

    /**
     * Checks emoji permissions and shows appropriate dialogs or proceeds with action.
     * Respects session dismissal by default.
     */
    fun checkPermissionsAndProceed(
        context: Context,
        onAllPermissionsGranted: () -> Unit,
        onPermissionsDenied: () -> Unit = {},
        showExplanation: Boolean = true,
        respectSessionDismissal: Boolean = true
    ) {
        BatteryLogger.d(TAG, "Checking emoji permissions and proceeding (respectSessionDismissal: $respectSessionDismissal)")
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_CHECK_AND_PROCEED")

        if (hasAllRequiredPermissions(context)) {
            BatteryLogger.d(EMOJI_PERMISSION_TAG, "All emoji permissions granted, proceeding")
            onAllPermissionsGranted()
            return
        }

        // Check if dialog should be shown considering session dismissal
        if (!shouldShowPermissionDialog(context, respectSessionDismissal)) {
            if (isDialogDismissedInCurrentSession()) {
                BatteryLogger.d(EMOJI_PERMISSION_TAG, "Dialog not shown - user dismissed dialog earlier in this session")
            } else {
                BatteryLogger.d(EMOJI_PERMISSION_TAG, "Dialog not shown - other condition preventing display")
            }
            onPermissionsDenied()
            return
        }

        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Emoji permissions missing, showing explanation: $showExplanation")
        if (showExplanation) {
            showPermissionExplanationDialog(
                context = context,
                onProceed = {
                    requestRequiredPermissions(context, onAllPermissionsGranted, onPermissionsDenied)
                },
                onCancel = {
                    BatteryLogger.d(EMOJI_PERMISSION_TAG, "User dismissed permission dialog - marking as dismissed for session")
                    // Mark dialog as dismissed when user cancels
                    markDialogDismissedForSession()
                    onPermissionsDenied()
                }
            )
        } else {
            requestRequiredPermissions(context, onAllPermissionsGranted, onPermissionsDenied)
        }
    }

    /**
     * Handles retry scenarios when permissions are still not granted after user returns from settings.
     * Provides user-friendly options to retry or get help.
     */
    fun handlePermissionRetry(
        context: Context,
        onRetryRequested: () -> Unit = {},
        onHelpRequested: () -> Unit = {},
        onCancelRetry: () -> Unit = {}
    ) {
        BatteryLogger.d(TAG, "Handling permission retry scenario")
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_RETRY_REQUESTED")

        // Log current permission status for debugging
        val permissionStatus = getPermissionStatusSummary(context)
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Permission status during retry: $permissionStatus")

        // Check if context is a FragmentActivity to show enhanced retry dialog
        if (context is FragmentActivity) {
            showEnhancedRetryDialog(context, onRetryRequested, onHelpRequested, onCancelRetry)
        } else {
            // Fallback to basic retry handling
            BatteryLogger.d(EMOJI_PERMISSION_TAG, "Non-activity context, triggering direct retry")
            onRetryRequested()
        }
    }

    /**
     * Shows an enhanced retry dialog with options to try again or get help
     */
    private fun showEnhancedRetryDialog(
        activity: FragmentActivity,
        onRetryRequested: () -> Unit,
        onHelpRequested: () -> Unit,
        onCancelRetry: () -> Unit
    ) {
        try {
            BatteryLogger.d(EMOJI_DIALOG_TAG, "Showing enhanced retry dialog")

            // For now, we'll use the same enhanced dialog but with different callbacks
            // In a full implementation, you might want a separate retry-specific dialog
            val dialog = AccessibilityPermissionDialogFragment.newInstance()

            dialog.onGrantPermissionClicked = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User requested retry from enhanced dialog")
                onRetryRequested()
            }

            dialog.onLearnMoreClicked = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User requested help from enhanced dialog")
                onHelpRequested()
            }

            dialog.onCancelClicked = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User cancelled retry from enhanced dialog")
                onCancelRetry()
            }

            dialog.show(activity.supportFragmentManager, "AccessibilityPermissionRetryDialog")

        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error showing enhanced retry dialog", exception)
            onRetryRequested() // Fallback to direct retry
        }
    }

    /**
     * Gets a summary of current emoji permission status for debugging.
     */
    fun getPermissionStatusSummary(context: Context): Map<String, Any> {
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Getting emoji permission status summary")

        val summary = mapOf(
            "hasAllRequiredPermissions" to hasAllRequiredPermissions(context),
            "isAccessibilityServiceEnabled" to isAccessibilityServiceEnabled(context),
            "isOverlayPermissionGranted" to OverlayPermissionUtils.isOverlayPermissionGranted(context),
            "androidApiLevel" to Build.VERSION.SDK_INT,
            "needsOverlayPermission" to (Build.VERSION.SDK_INT < Build.VERSION_CODES.O),
            "serviceName" to "${context.packageName}/${EmojiBatteryAccessibilityService::class.java.name}"
        )

        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_STATUS: $summary")
        return summary
    }
}
